# 渗透测试靶场 - Apache配置文件

# 启用重写引擎
RewriteEngine On

# 安全配置
# 隐藏Apache版本信息
ServerTokens Prod
ServerSignature Off

# 防止访问敏感文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 保护配置文件
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

<Files "config/*">
    Order allow,deny
    Deny from all
</Files>

# 设置默认首页
DirectoryIndex index.php index.html

# 错误页面配置
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# 文件上传安全配置
# 防止执行上传目录中的PHP文件（可以被绕过，这是故意的漏洞）
<Directory "upload">
    # 注意：这个配置故意设置得不够严格，可以被绕过
    <Files "*.php">
        # Order allow,deny
        # Deny from all
    </Files>
</Directory>

# MIME类型配置
AddType application/x-httpd-php .php
AddType text/html .html

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# 压缩配置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 安全头配置（故意设置得不够严格）
<IfModule mod_headers.c>
    # X-Frame-Options (故意注释掉，存在点击劫持风险)
    # Header always append X-Frame-Options SAMEORIGIN
    
    # X-Content-Type-Options
    Header set X-Content-Type-Options nosniff
    
    # X-XSS-Protection (故意设置为0，禁用XSS保护)
    Header set X-XSS-Protection "0"
    
    # Content-Security-Policy (故意设置得很宽松)
    Header set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' *"
</IfModule>

# URL重写规则
RewriteRule ^admin/?$ dashboard.php [L]
RewriteRule ^profile/?$ profile.php [L]
RewriteRule ^upload/?$ upload.php [L]

# 故意的安全漏洞配置
# 1. 允许.htaccess文件被访问（信息泄露）
<Files ".htaccess">
    Order allow,deny
    Allow from all
</Files>

# 2. 允许备份文件被访问
<Files "*.bak">
    Order allow,deny
    Allow from all
</Files>

<Files "*.backup">
    Order allow,deny
    Allow from all
</Files>

# 3. 允许源码文件被访问
<Files "*.txt">
    Order allow,deny
    Allow from all
</Files>

# 开发环境配置（生产环境应该移除）
php_flag display_errors On
php_flag display_startup_errors On
php_value error_reporting E_ALL

# 文件上传配置
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300
