<?php
session_start();
require_once 'config/database.php';

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// 获取用户信息
$db = getDatabase();
$conn = $db->getConnection();

$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// 获取统计信息
$stats = [];

// 用户总数
$stmt = $conn->prepare("SELECT COUNT(*) as total_users FROM users");
$stmt->execute();
$stats['total_users'] = $stmt->fetch()['total_users'];

// 上传文件总数
$stmt = $conn->prepare("SELECT COUNT(*) as total_uploads FROM uploads");
$stmt->execute();
$stats['total_uploads'] = $stmt->fetch()['total_uploads'];

// 今日登录次数
$stmt = $conn->prepare("SELECT COUNT(*) as today_logins FROM system_logs WHERE action = 'login_success' AND DATE(created_at) = CURDATE()");
$stmt->execute();
$stats['today_logins'] = $stmt->fetch()['today_logins'];

// 最近上传的文件
$stmt = $conn->prepare("SELECT u.*, us.username FROM uploads u JOIN users us ON u.user_id = us.id ORDER BY u.upload_time DESC LIMIT 5");
$stmt->execute();
$recent_uploads = $stmt->fetchAll();

// 处理退出登录
if (isset($_GET['logout'])) {
    logSystemAction('logout', $_SESSION['user_id'], 'User logged out');
    session_destroy();
    header('Location: login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渗透测试靶场 - 后台管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="brand-text">渗透测试靶场</span>
            </div>
            <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user"></i>
                        <span>个人资料</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="upload.php">
                        <i class="fas fa-upload"></i>
                        <span>文件上传</span>
                    </a>
                </li>
                <?php if ($user['role'] == 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-list-alt"></i>
                        <span>系统日志</span>
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" onerror="this.src='assets/images/default-avatar.png'">
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                    <div class="user-role"><?php echo $user['role'] == 'admin' ? '管理员' : '用户'; ?></div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">仪表板</h1>
            </div>
            
            <div class="topbar-right">
                <div class="topbar-item">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                </div>
                
                <div class="topbar-item dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" class="topbar-avatar" onerror="this.src='assets/images/default-avatar.png'">
                        <span class="ms-2"><?php echo htmlspecialchars($user['username']); ?></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </header>
        
        <!-- 页面内容 -->
        <main class="content">
            <div class="container-fluid">
                <!-- 欢迎信息 -->
                <div class="welcome-section">
                    <div class="row">
                        <div class="col-12">
                            <div class="welcome-card">
                                <div class="welcome-content">
                                    <h2>欢迎回来，<?php echo htmlspecialchars($user['username']); ?>！</h2>
                                    <p>今天是 <?php echo date('Y年m月d日 H:i'); ?>，祝您使用愉快。</p>
                                </div>
                                <div class="welcome-actions">
                                    <a href="upload.php" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>上传文件
                                    </a>
                                    <a href="profile.php" class="btn btn-outline-primary">
                                        <i class="fas fa-user me-2"></i>编辑资料
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-section">
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stats-card stats-primary">
                                <div class="stats-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number"><?php echo $stats['total_users']; ?></div>
                                    <div class="stats-label">总用户数</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stats-card stats-success">
                                <div class="stats-icon">
                                    <i class="fas fa-file-upload"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number"><?php echo $stats['total_uploads']; ?></div>
                                    <div class="stats-label">上传文件数</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stats-card stats-warning">
                                <div class="stats-icon">
                                    <i class="fas fa-sign-in-alt"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number"><?php echo $stats['today_logins']; ?></div>
                                    <div class="stats-label">今日登录</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="stats-card stats-info">
                                <div class="stats-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stats-content">
                                    <div class="stats-number"><?php echo date('H:i'); ?></div>
                                    <div class="stats-label">当前时间</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近上传 -->
                <div class="recent-section">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history me-2"></i>最近上传的文件
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_uploads)): ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">暂无上传记录</p>
                                            <a href="upload.php" class="btn btn-primary">立即上传</a>
                                        </div>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>文件名</th>
                                                        <th>上传者</th>
                                                        <th>文件大小</th>
                                                        <th>上传时间</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($recent_uploads as $upload): ?>
                                                    <tr>
                                                        <td>
                                                            <i class="fas fa-file me-2"></i>
                                                            <?php echo htmlspecialchars($upload['original_name']); ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($upload['username']); ?></td>
                                                        <td><?php echo formatFileSize($upload['file_size']); ?></td>
                                                        <td><?php echo date('Y-m-d H:i', strtotime($upload['upload_time'])); ?></td>
                                                        <td>
                                                            <a href="<?php echo htmlspecialchars($upload['file_path']); ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>

<?php
/**
 * 格式化文件大小
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}
?>
