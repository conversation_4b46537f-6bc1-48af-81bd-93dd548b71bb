<?php
session_start();
require_once 'config/database.php';

// 如果已经登录，重定向到后台
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (!empty($username) && !empty($password)) {
        // 故意使用不安全的SQL查询（存在布尔盲注漏洞）
        $db = getDatabase();
        $unsafe_conn = $db->getUnsafeConnection();
        
        // 不安全的查询 - 存在SQL注入漏洞
        $password_md5 = md5($password);
        $query = "SELECT * FROM users WHERE username = '$username' AND password = '$password_md5'";
        
        // 记录登录尝试
        logSystemAction('login_attempt', null, "Username: $username");
        
        $result = mysqli_query($unsafe_conn, $query);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            
            // 设置会话
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['avatar'] = $user['avatar'];
            
            // 记录成功登录
            logSystemAction('login_success', $user['id'], "Successful login");
            
            header('Location: dashboard.php');
            exit();
        } else {
            $error_message = '用户名或密码错误！';
            logSystemAction('login_failed', null, "Failed login attempt for: $username");
        }
        
        mysqli_close($unsafe_conn);
    } else {
        $error_message = '请输入用户名和密码！';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渗透测试靶场 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/login.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card">
                        <div class="login-header text-center">
                            <div class="logo-container">
                                <i class="fas fa-shield-alt logo-icon"></i>
                            </div>
                            <h2 class="login-title">渗透测试靶场</h2>
                            <p class="login-subtitle">安全测试学习平台</p>
                        </div>
                        
                        <div class="login-body">
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo htmlspecialchars($success_message); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="" class="login-form">
                                <div class="form-group mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>用户名
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           placeholder="请输入用户名" required>
                                </div>
                                
                                <div class="form-group mb-4">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>密码
                                    </label>
                                    <div class="password-input-group">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="请输入密码" required>
                                        <button type="button" class="password-toggle" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="password-icon"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember">
                                        <label class="form-check-label" for="remember">
                                            记住我
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-login w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>登录
                                </button>
                            </form>
                        </div>
                        
                        <div class="login-footer text-center">
                            <div class="demo-accounts">
                                <h6>测试账户</h6>
                                <small class="text-muted">
                                    管理员: admin/admin123 | 用户: test/test123
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/login.js"></script>
</body>
</html>
