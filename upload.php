<?php
session_start();
require_once 'config/database.php';

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// 获取用户信息
$db = getDatabase();
$conn = $db->getConnection();

$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

$message = '';
$message_type = '';

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['upload_file'])) {
    $upload_dir = 'upload/';
    $file = $_FILES['upload_file'];
    
    // 确保上传目录存在
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $original_name = $file['name'];
        $file_size = $file['size'];
        $tmp_name = $file['tmp_name'];
        
        // 生成唯一文件名
        $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $unique_name = uniqid() . '_' . time() . '.' . $file_extension;
        $file_path = $upload_dir . $unique_name;
        
        // 服务端验证（故意设置得很宽松，容易绕过）
        $max_size = 10 * 1024 * 1024; // 10MB
        if ($file_size > $max_size) {
            $message = '文件大小不能超过10MB！';
            $message_type = 'danger';
        } else {
            // 移动文件到上传目录
            if (move_uploaded_file($tmp_name, $file_path)) {
                // 记录上传信息到数据库
                $stmt = $conn->prepare("INSERT INTO uploads (user_id, filename, original_name, file_path, file_size) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$_SESSION['user_id'], $unique_name, $original_name, $file_path, $file_size]);
                
                // 如果是头像上传，更新用户头像
                if (isset($_POST['is_avatar']) && $_POST['is_avatar'] == '1') {
                    $stmt = $conn->prepare("UPDATE users SET avatar = ? WHERE id = ?");
                    $stmt->execute([$unique_name, $_SESSION['user_id']]);
                    $_SESSION['avatar'] = $unique_name;
                }
                
                // 记录系统日志
                logSystemAction('file_upload', $_SESSION['user_id'], "Uploaded file: $original_name");
                
                $message = '文件上传成功！';
                $message_type = 'success';
            } else {
                $message = '文件上传失败，请重试！';
                $message_type = 'danger';
            }
        }
    } else {
        $message = '文件上传出错：' . $file['error'];
        $message_type = 'danger';
    }
}

// 获取用户的上传历史
$stmt = $conn->prepare("SELECT * FROM uploads WHERE user_id = ? ORDER BY upload_time DESC LIMIT 10");
$stmt->execute([$_SESSION['user_id']]);
$user_uploads = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传 - 渗透测试靶场</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
    <link href="assets/css/upload.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="brand-text">渗透测试靶场</span>
            </div>
            <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user"></i>
                        <span>个人资料</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="upload.php">
                        <i class="fas fa-upload"></i>
                        <span>文件上传</span>
                    </a>
                </li>
                <?php if ($user['role'] == 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-list-alt"></i>
                        <span>系统日志</span>
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" onerror="this.src='assets/images/default-avatar.png'">
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                    <div class="user-role"><?php echo $user['role'] == 'admin' ? '管理员' : '用户'; ?></div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">文件上传</h1>
            </div>
            
            <div class="topbar-right">
                <div class="topbar-item">
                    <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-arrow-left me-2"></i>返回仪表板
                    </a>
                </div>
                
                <div class="topbar-item dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" class="topbar-avatar" onerror="this.src='assets/images/default-avatar.png'">
                        <span class="ms-2"><?php echo htmlspecialchars($user['username']); ?></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="dashboard.php?logout=1"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </header>
        
        <!-- 页面内容 -->
        <main class="content">
            <div class="container-fluid">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- 文件上传区域 -->
                    <div class="col-lg-8">
                        <div class="card upload-card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cloud-upload-alt me-2"></i>文件上传
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="uploadForm" method="POST" enctype="multipart/form-data" class="upload-form">
                                    <div class="upload-area" id="uploadArea">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="upload-text">
                                            <h4>拖拽文件到此处或点击选择文件</h4>
                                            <p>支持多种文件格式，最大10MB</p>
                                        </div>
                                        <input type="file" id="fileInput" name="upload_file" class="file-input" accept="*/*">
                                    </div>
                                    
                                    <div class="upload-options mt-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isAvatar" name="is_avatar" value="1">
                                            <label class="form-check-label" for="isAvatar">
                                                设置为头像
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="upload-progress mt-3" id="uploadProgress" style="display: none;">
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <div class="progress-text">上传中...</div>
                                    </div>
                                    
                                    <div class="upload-actions mt-3">
                                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                                            <i class="fas fa-upload me-2"></i>上传文件
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="clearUpload()">
                                            <i class="fas fa-times me-2"></i>清除
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 上传提示 -->
                    <div class="col-lg-4">
                        <div class="card info-card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>上传说明
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>支持图片、文档等多种格式</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    <span>文件大小限制：10MB</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-shield-alt text-info me-2"></i>
                                    <span>系统会自动检测文件类型</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock text-muted me-2"></i>
                                    <span>上传后可在历史记录中查看</span>
                                </div>
                                
                                <hr>
                                
                                <h6 class="text-muted">安全提示</h6>
                                <small class="text-muted">
                                    为了系统安全，某些文件类型可能被限制上传。
                                    如果遇到问题，请联系管理员。
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 上传历史 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-history me-2"></i>上传历史
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($user_uploads)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无上传记录</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>文件名</th>
                                                    <th>文件大小</th>
                                                    <th>上传时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($user_uploads as $upload): ?>
                                                <tr>
                                                    <td>
                                                        <i class="fas fa-file me-2"></i>
                                                        <?php echo htmlspecialchars($upload['original_name']); ?>
                                                    </td>
                                                    <td><?php echo formatFileSize($upload['file_size']); ?></td>
                                                    <td><?php echo date('Y-m-d H:i', strtotime($upload['upload_time'])); ?></td>
                                                    <td>
                                                        <a href="<?php echo htmlspecialchars($upload['file_path']); ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteFile(<?php echo $upload['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/upload.js"></script>
</body>
</html>

<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}
?>
