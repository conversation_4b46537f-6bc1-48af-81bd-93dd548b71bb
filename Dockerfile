# 渗透测试靶场 - Docker镜像
FROM php:8.1-apache

# 设置维护者信息
LABEL maintainer="<EMAIL>"
LABEL description="Penetration Testing Lab - Web Security Training Platform"
LABEL version="1.0"

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    mariadb-client \
    unzip \
    curl \
    vim \
    && rm -rf /var/lib/apt/lists/*

# 配置PHP扩展
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        gd \
        pdo \
        pdo_mysql \
        mysqli \
        mbstring \
        xml \
        zip \
        opcache

# 启用Apache模块
RUN a2enmod rewrite headers expires deflate

# 设置PHP配置
RUN { \
    echo 'upload_max_filesize = 10M'; \
    echo 'post_max_size = 10M'; \
    echo 'max_execution_time = 300'; \
    echo 'max_input_time = 300'; \
    echo 'memory_limit = 256M'; \
    echo 'display_errors = On'; \
    echo 'display_startup_errors = On'; \
    echo 'error_reporting = E_ALL'; \
    echo 'log_errors = On'; \
    echo 'error_log = /var/log/apache2/php_errors.log'; \
} > /usr/local/etc/php/conf.d/pentest-lab.ini

# 设置Apache配置
COPY docker/apache-config.conf /etc/apache2/sites-available/000-default.conf

# 创建应用目录
WORKDIR /var/www/html

# 复制应用文件
COPY . /var/www/html/

# 创建必要的目录并设置权限
RUN mkdir -p /var/www/html/upload \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/upload

# 创建启动脚本
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 设置入口点
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["apache2-foreground"]
