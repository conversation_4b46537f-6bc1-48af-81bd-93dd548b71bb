/* 渗透测试靶场 - 文件上传页面样式 */

.upload-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.upload-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 35px rgba(0,0,0,0.15);
}

.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.05);
}

.upload-area.dragover {
    border-color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
    color: var(--secondary-color);
    transform: scale(1.1);
}

.upload-text h4 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.upload-text p {
    color: #6c757d;
    margin: 0;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-options {
    padding: 20px;
    background: rgba(52, 152, 219, 0.05);
    border-radius: 8px;
}

.upload-progress {
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, var(--secondary-color), #5dade2);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-size: 0.9rem;
    color: #6c757d;
}

.upload-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.info-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

/* 文件预览样式 */
.file-preview {
    display: none;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.file-preview.show {
    display: block;
    animation: fadeInUp 0.3s ease;
}

.preview-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 10px;
}

.preview-icon {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.preview-info {
    flex: 1;
}

.preview-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 2px;
}

.preview-size {
    font-size: 0.8rem;
    color: #6c757d;
}

.preview-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.preview-remove:hover {
    background: rgba(220, 53, 69, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-area {
        padding: 40px 15px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .upload-text h4 {
        font-size: 1.2rem;
    }
    
    .upload-actions {
        flex-direction: column;
    }
    
    .upload-actions .btn {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.upload-area.uploading {
    animation: pulse 1.5s infinite;
}

/* 文件类型图标颜色 */
.file-type-image .preview-icon {
    background: #28a745;
}

.file-type-document .preview-icon {
    background: #17a2b8;
}

.file-type-archive .preview-icon {
    background: #ffc107;
    color: #212529;
}

.file-type-video .preview-icon {
    background: #6f42c1;
}

.file-type-audio .preview-icon {
    background: #fd7e14;
}

.file-type-code .preview-icon {
    background: #20c997;
}

.file-type-executable .preview-icon {
    background: #dc3545;
}
