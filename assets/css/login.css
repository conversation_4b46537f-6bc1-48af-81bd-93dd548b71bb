/* 渗透测试靶场 - 登录页面样式 */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --dark-color: #34495e;
    --light-color: #ecf0f1;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 25px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 40px rgba(0,0,0,0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--gradient-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.login-container {
    width: 100%;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: var(--shadow-heavy);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0,0,0,0.25);
}

.login-header {
    padding: 40px 30px 20px;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.1) 0%, rgba(52, 73, 94, 0.1) 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.logo-container {
    margin-bottom: 20px;
}

.logo-icon {
    font-size: 3.5rem;
    color: var(--secondary-color);
    text-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.login-title {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 8px;
    text-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.login-subtitle {
    color: #7f8c8d;
    font-size: 0.95rem;
    font-weight: 400;
    margin: 0;
}

.login-body {
    padding: 30px;
}

.form-label {
    color: var(--dark-color);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background: white;
    transform: translateY(-1px);
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

.form-check-input:checked {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-login {
    background: var(--gradient-primary);
    border: none;
    border-radius: 12px;
    padding: 14px 20px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-login:active {
    transform: translateY(0);
}

.login-footer {
    padding: 20px 30px 30px;
    background: rgba(0,0,0,0.02);
    border-top: 1px solid rgba(0,0,0,0.05);
}

.demo-accounts h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 8px;
}

.demo-accounts small {
    font-size: 0.8rem;
    line-height: 1.4;
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 12px 16px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%);
    color: #c0392b;
    border-left: 4px solid #e74c3c;
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(34, 153, 84, 0.1) 100%);
    color: #229954;
    border-left: 4px solid #27ae60;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-container {
        padding: 10px;
    }
    
    .login-card {
        border-radius: 15px;
    }
    
    .login-header {
        padding: 30px 20px 15px;
    }
    
    .login-body {
        padding: 20px;
    }
    
    .login-footer {
        padding: 15px 20px 20px;
    }
    
    .logo-icon {
        font-size: 3rem;
    }
    
    .login-title {
        font-size: 1.6rem;
    }
}

/* 动画效果 */
.login-card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
