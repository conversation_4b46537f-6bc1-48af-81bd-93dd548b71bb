/**
 * 渗透测试靶场 - 登录页面JavaScript
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeLoginPage();
});

/**
 * 初始化登录页面
 */
function initializeLoginPage() {
    // 添加输入框焦点效果
    addInputFocusEffects();
    
    // 添加表单验证
    addFormValidation();
    
    // 添加键盘事件
    addKeyboardEvents();
    
    // 添加动画效果
    addAnimationEffects();
    
    // 自动关闭警告框
    autoCloseAlerts();
}

/**
 * 添加输入框焦点效果
 */
function addInputFocusEffects() {
    const inputs = document.querySelectorAll('.form-control');
    
    inputs.forEach(input => {
        // 焦点进入
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            addRippleEffect(this);
        });
        
        // 焦点离开
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        // 输入时的效果
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
}

/**
 * 添加波纹效果
 */
function addRippleEffect(element) {
    const ripple = document.createElement('span');
    ripple.classList.add('ripple');
    element.parentElement.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * 切换密码显示/隐藏
 */
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
    
    // 添加点击动画
    passwordIcon.style.transform = 'scale(0.8)';
    setTimeout(() => {
        passwordIcon.style.transform = 'scale(1)';
    }, 150);
}

/**
 * 添加表单验证
 */
function addFormValidation() {
    const form = document.querySelector('.login-form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // 清除之前的错误状态
        clearValidationErrors();
        
        // 验证用户名
        if (!usernameInput.value.trim()) {
            showFieldError(usernameInput, '请输入用户名');
            isValid = false;
        } else if (usernameInput.value.length < 3) {
            showFieldError(usernameInput, '用户名至少3个字符');
            isValid = false;
        }
        
        // 验证密码
        if (!passwordInput.value.trim()) {
            showFieldError(passwordInput, '请输入密码');
            isValid = false;
        } else if (passwordInput.value.length < 6) {
            showFieldError(passwordInput, '密码至少6个字符');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            shakeForm();
        } else {
            showLoadingState();
        }
    });
}

/**
 * 显示字段错误
 */
function showFieldError(input, message) {
    input.classList.add('is-invalid');
    
    // 创建错误消息元素
    const errorDiv = document.createElement('div');
    errorDiv.classList.add('invalid-feedback');
    errorDiv.textContent = message;
    
    // 插入错误消息
    input.parentElement.appendChild(errorDiv);
}

/**
 * 清除验证错误
 */
function clearValidationErrors() {
    const invalidInputs = document.querySelectorAll('.is-invalid');
    const errorMessages = document.querySelectorAll('.invalid-feedback');
    
    invalidInputs.forEach(input => input.classList.remove('is-invalid'));
    errorMessages.forEach(msg => msg.remove());
}

/**
 * 表单震动效果
 */
function shakeForm() {
    const form = document.querySelector('.login-card');
    form.style.animation = 'shake 0.5s ease-in-out';
    
    setTimeout(() => {
        form.style.animation = '';
    }, 500);
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    const submitBtn = document.querySelector('.btn-login');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登录中...';
    submitBtn.disabled = true;
    
    // 如果3秒后还没有响应，恢复按钮状态
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
}

/**
 * 添加键盘事件
 */
function addKeyboardEvents() {
    // Enter键提交表单
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const form = document.querySelector('.login-form');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
    });
    
    // Escape键清除表单
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            clearForm();
        }
    });
}

/**
 * 清除表单
 */
function clearForm() {
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.value = '';
        input.classList.remove('has-value');
    });
    clearValidationErrors();
}

/**
 * 添加动画效果
 */
function addAnimationEffects() {
    // 为表单元素添加延迟动画
    const formElements = document.querySelectorAll('.form-group, .btn-login');
    formElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.5s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 100 * (index + 1));
    });
}

/**
 * 自动关闭警告框
 */
function autoCloseAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentElement) {
                alert.style.transition = 'all 0.5s ease';
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        }, 5000);
    });
}

// 添加CSS动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(52, 152, 219, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc3545;
    }
    
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
`;
document.head.appendChild(style);
